const AliAsrDemo = require('./ali_asr_demo');
const fs = require('fs');

// 测试配置
const TEST_CONFIG = {
    // 请替换为您的实际配置
    APPKEY: 'your_appkey_here',
    TOKEN: 'your_token_here',
    // 测试音频文件路径
    AUDIO_FILE: './test_audio.wav'
};

async function testAliAsr() {
    console.log('🧪 开始测试阿里云ASR Demo');
    
    // 检查配置
    if (TEST_CONFIG.APPKEY === 'your_appkey_here' || TEST_CONFIG.TOKEN === 'your_token_here') {
        console.error('❌ 请先在test_ali_asr.js中配置正确的AppKey和Token');
        console.log('💡 请编辑TEST_CONFIG中的APPKEY和TOKEN变量');
        return;
    }
    
    // 检查测试音频文件
    if (!fs.existsSync(TEST_CONFIG.AUDIO_FILE)) {
        console.error(`❌ 测试音频文件不存在: ${TEST_CONFIG.AUDIO_FILE}`);
        console.log('💡 请准备一个WAV格式的测试音频文件');
        console.log('💡 音频要求: WAV格式, 16kHz采样率, PCM编码');
        return;
    }
    
    console.log('📁 测试音频文件:', TEST_CONFIG.AUDIO_FILE);
    
    // 创建ASR实例
    const asrDemo = new AliAsrDemo(TEST_CONFIG.APPKEY, TEST_CONFIG.TOKEN);
    
    try {
        console.log('🚀 开始语音识别测试...');
        const result = await asrDemo.recognizeWavFile(TEST_CONFIG.AUDIO_FILE);
        
        console.log('\n✅ 测试完成！');
        console.log('📝 识别结果:', result || '未识别到内容');
        
        if (result && result.length > 0) {
            console.log('🎉 测试成功！ASR服务工作正常');
        } else {
            console.log('⚠️  未识别到内容，请检查音频文件质量');
        }
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.log('\n🔧 故障排查建议:');
        console.log('1. 检查网络连接是否正常');
        console.log('2. 确认AppKey和Token是否正确');
        console.log('3. 检查音频文件格式是否符合要求');
        console.log('4. 确认阿里云服务是否正常');
    }
}

// 运行测试
if (require.main === module) {
    testAliAsr().catch(console.error);
}

module.exports = { testAliAsr, TEST_CONFIG };
