# 🐛 Bug修复：阿里云ASR Message ID格式错误

## 问题描述

在使用阿里云ASR服务时遇到以下错误：

```json
{
  "header": {
    "namespace": "Default",
    "name": "TaskFailed",
    "status": 40000002,
    "message_id": "1c147d22adc443049c4bd6e29dfdc197",
    "task_id": "24058cc369a8584f83d7339b116a8e69",
    "status_text": "Gateway:MESSAGE_INVALID:Invalid message id '799aca8e-d1f5-4839-b476-f4421f4fc6c0'!"
  }
}
```

## 错误原因

根据阿里云文档说明：
- WebSocket接口要求 `message_id` 必须是**32位十六进制字符串**
- 原代码使用的UUID格式包含连字符，不符合要求
- 错误的格式：`799aca8e-d1f5-4839-b476-f4421f4fc6c0` (包含连字符)
- 正确的格式：`799aca8ed1f54839b476f4421f4fc6c0` (32位纯十六进制)

## 修复方案

### 修复前的代码
```javascript
// 错误的UUID生成方法
generateUUID() {
    return crypto.randomUUID().replace(/-/g, '');
}

generateSimpleUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}
```

### 修复后的代码
```javascript
// 正确的ID生成方法
generateMessageId() {
    // 生成32个十六进制字符
    return crypto.randomBytes(16).toString('hex');
}

generateTaskId() {
    return crypto.randomBytes(16).toString('hex');
}
```

## 修复验证

创建了测试脚本 `test_id_generation.js` 来验证ID格式：

```bash
node test_id_generation.js
```

输出示例：
```
🧪 测试阿里云ASR所需的ID格式
==================================================

测试 1:
Message ID: 4c2f8bbcb3deb46e43b16968e514d40c
  - 长度: 32 字符
  - 格式: ✅ 正确
Task ID: 719604200e521040e838eaaee641e4c6
  - 长度: 32 字符
  - 格式: ✅ 正确
```

## 格式要求总结

阿里云ASR WebSocket接口的ID格式要求：

| 字段 | 要求 | 示例 |
|------|------|------|
| message_id | 32位十六进制字符串 | `4c2f8bbcb3deb46e43b16968e514d40c` |
| task_id | 32位十六进制字符串 | `719604200e521040e838eaaee641e4c6` |

### 格式规则
- ✅ 必须是32个字符
- ✅ 只能包含0-9和a-f的十六进制字符
- ❌ 不能包含连字符(-)
- ❌ 不能包含其他特殊字符
- ❌ 不能使用标准UUID格式

## 修复的文件

1. **`ali_asr_demo.js`** - 主要修复文件
   - 更新了ID生成方法
   - 添加了调试日志显示生成的ID格式

2. **`test_id_generation.js`** - 新增测试文件
   - 验证ID生成格式是否正确

3. **`README_ali_asr_demo.md`** - 更新文档
   - 添加了错误排查说明

4. **`BUGFIX_MESSAGE_ID.md`** - 本修复说明文档

## 测试建议

在使用修复后的代码前，建议：

1. 运行ID格式测试：
   ```bash
   node test_id_generation.js
   ```

2. 检查生成的ID是否符合要求：
   - 长度为32字符
   - 只包含0-9和a-f字符
   - 没有连字符或其他特殊字符

3. 测试ASR功能：
   ```bash
   node ali_asr_demo.js ./test.wav
   ```

## 相关参考

- [阿里云智能语音交互文档](https://help.aliyun.com/document_detail/84428.html)
- [WebSocket实时语音识别接口说明](https://help.aliyun.com/document_detail/84428.html)
- 错误码40000002：Gateway:MESSAGE_INVALID

---

✅ **修复状态**: 已完成  
🕒 **修复时间**: 2025年1月  
🔧 **修复类型**: ID格式规范化
