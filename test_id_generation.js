const crypto = require('crypto');

// 测试ID生成函数
function generateMessageId() {
    return crypto.randomBytes(16).toString('hex');
}

function generateTaskId() {
    return crypto.randomBytes(16).toString('hex');
}

console.log('🧪 测试阿里云ASR所需的ID格式');
console.log('=' .repeat(50));

// 生成多个ID进行测试
for (let i = 1; i <= 5; i++) {
    const messageId = generateMessageId();
    const taskId = generateTaskId();
    
    console.log(`\n测试 ${i}:`);
    console.log(`Message ID: ${messageId}`);
    console.log(`  - 长度: ${messageId.length} 字符`);
    console.log(`  - 格式: ${/^[0-9a-f]{32}$/.test(messageId) ? '✅ 正确' : '❌ 错误'}`);
    
    console.log(`Task ID: ${taskId}`);
    console.log(`  - 长度: ${taskId.length} 字符`);
    console.log(`  - 格式: ${/^[0-9a-f]{32}$/.test(taskId) ? '✅ 正确' : '❌ 错误'}`);
}

console.log('\n📋 格式要求:');
console.log('- 必须是32个字符');
console.log('- 只能包含0-9和a-f的十六进制字符');
console.log('- 不能包含连字符或其他特殊字符');

console.log('\n✅ 测试完成！如果所有ID都显示"正确"，则格式符合阿里云要求。');
