# 🐛 Bug修复：Task ID不匹配错误

## 问题描述

在使用阿里云ASR服务时遇到以下错误：

```json
{
  "header": {
    "namespace": "Default",
    "name": "TaskFailed",
    "status": 40000002,
    "message_id": "d809e458b40443358258eea08efca4a8",
    "task_id": "28e43c0478091f60c76d0d111b80256e",
    "status_text": "Gateway:MESSAGE_INVALID:The task id '89db7a0a88b39ddeb9001c5a5cb33215' of this message doesn't match last message '28e43c0478091f60c76d0d111b80256e'!"
  }
}
```

## 错误原因

阿里云ASR服务要求在整个WebSocket会话中使用**相同的task_id**：

1. **StartTranscription** 消息使用一个task_id
2. **StopTranscription** 消息必须使用**相同的task_id**
3. 原代码在发送停止消息时生成了新的task_id，导致不匹配

## 修复方案

### 修复前的代码

#### Node.js版本
```javascript
class AliAsrDemo {
    constructor(appkey, token) {
        // ... 其他属性
        this.taskId = this.generateTaskId(); // ❌ 错误：预生成task_id
    }
    
    // 发送停止消息时
    const stopMessage = {
        header: {
            task_id: this.generateTaskId(), // ❌ 错误：生成新的task_id
            message_id: this.generateMessageId()
        }
    };
}
```

#### Go版本
```go
// 发送停止消息时
stopMessage := AsrMessage{
    Header: MessageHeader{
        TaskID:    generateHexID(), // ❌ 错误：生成新的task_id
        MessageID: generateHexID(),
    },
}
```

### 修复后的代码

#### Node.js版本
```javascript
class AliAsrDemo {
    constructor(appkey, token) {
        // ... 其他属性
        this.currentTaskId = null; // ✅ 正确：初始化为null
    }
    
    // 连接时保存task_id
    const taskId = this.generateTaskId();
    this.currentTaskId = taskId; // ✅ 正确：保存task_id
    
    // 发送停止消息时
    const stopMessage = {
        header: {
            task_id: this.currentTaskId, // ✅ 正确：使用相同的task_id
            message_id: this.generateMessageId() // message_id可以不同
        }
    };
}
```

#### Go版本
```go
type AliAsrDemo struct {
    // ... 其他字段
    currentTaskID string // ✅ 正确：添加字段保存task_id
}

// 连接时保存task_id
taskID := generateHexID()
asr.currentTaskID = taskID // ✅ 正确：保存task_id

// 发送停止消息时
stopMessage := AsrMessage{
    Header: MessageHeader{
        TaskID:    asr.currentTaskID, // ✅ 正确：使用相同的task_id
        MessageID: generateHexID(),   // message_id可以不同
    },
}
```

## 修复的文件

### Node.js版本
1. **`ali_asr_demo.js`**
   - 添加 `currentTaskId` 属性
   - 在连接时保存task_id
   - 在停止时使用相同的task_id

### Go版本
1. **`ali_asr_demo.go`**
   - 添加 `currentTaskID` 字段
   - 在连接时保存task_id
   - 在停止时使用相同的task_id

## 阿里云ASR协议要求

### Task ID规则
- ✅ **StartTranscription** 和 **StopTranscription** 必须使用相同的task_id
- ✅ task_id必须是32位十六进制字符串
- ✅ 每个新的会话可以使用不同的task_id

### Message ID规则
- ✅ 每条消息的message_id可以不同
- ✅ message_id必须是32位十六进制字符串
- ✅ message_id用于标识单个消息

### 正确的消息流程
```
1. StartTranscription  -> task_id: "abc123...", message_id: "def456..."
2. [发送音频数据]      -> (二进制数据，无需ID)
3. StopTranscription   -> task_id: "abc123...", message_id: "ghi789..."
                          ↑ 相同的task_id    ↑ 不同的message_id
```

## 测试验证

### 验证步骤
1. **运行修复后的代码**
   ```bash
   # Node.js版本
   node ali_asr_demo.js ./test.wav
   
   # Go版本
   go run ali_asr_demo.go ./test.wav
   ```

2. **检查日志输出**
   - 应该看到相同的Task ID在开始和结束时使用
   - 不应该再出现"task id doesn't match"错误

3. **预期输出**
   ```
   🔑 生成的Task ID: abc123... (长度: 32)
   🔑 生成的Message ID: def456... (长度: 32)
   📤 已发送开始转录请求
   ...
   📤 已发送停止转录请求  // 使用相同的Task ID
   ✅ 转录完成
   ```

## 相关错误码

| 错误码 | 错误信息 | 原因 | 解决方案 |
|--------|----------|------|----------|
| 40000002 | Gateway:MESSAGE_INVALID:Invalid message id | message_id格式错误 | 使用32位十六进制 |
| 40000002 | Gateway:MESSAGE_INVALID:The task id doesn't match | task_id不匹配 | 使用相同的task_id |

## 最佳实践

### 1. ID管理
```javascript
// ✅ 推荐做法
class AsrClient {
    constructor() {
        this.currentTaskId = null;
    }
    
    startSession() {
        this.currentTaskId = this.generateTaskId();
        // 使用 this.currentTaskId
    }
    
    stopSession() {
        // 使用 this.currentTaskId (相同的ID)
    }
}
```

### 2. 错误处理
```javascript
// ✅ 添加错误检查
if (message.header.name === "TaskFailed") {
    console.error(`任务失败: ${message.header.status_text}`);
    if (message.header.status === 40000002) {
        console.error("可能是ID格式或匹配问题");
    }
}
```

### 3. 调试信息
```javascript
// ✅ 添加调试日志
console.log(`开始会话 - Task ID: ${this.currentTaskId}`);
console.log(`结束会话 - Task ID: ${this.currentTaskId}`);
```

## 总结

这个修复解决了阿里云ASR服务中task_id不匹配的问题，确保：

1. ✅ 整个会话使用相同的task_id
2. ✅ 每条消息使用不同的message_id
3. ✅ 所有ID都是32位十六进制格式
4. ✅ 符合阿里云ASR协议要求

修复后，应该不会再出现"task id doesn't match"错误。

---

✅ **修复状态**: 已完成  
🕒 **修复时间**: 2025年1月  
🔧 **修复类型**: 协议一致性修复  
📁 **影响文件**: `ali_asr_demo.js`, `ali_asr_demo.go`
