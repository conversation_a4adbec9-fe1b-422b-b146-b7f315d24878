package main

import (
	"crypto/rand"
	"encoding/binary"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"
	"net/url"
	"os"
	"strings"
	"time"

	"github.com/gorilla/websocket"
)

// ASR消息结构
type AsrMessage struct {
	Header  MessageHeader `json:"header"`
	Payload interface{}   `json:"payload,omitempty"`
}

type MessageHeader struct {
	AppKey     string `json:"appkey"`
	Namespace  string `json:"namespace"`
	Name       string `json:"name"`
	TaskID     string `json:"task_id"`
	MessageID  string `json:"message_id"`
	Status     int    `json:"status,omitempty"`
	StatusText string `json:"status_text,omitempty"`
}

type StartTranscriptionPayload struct {
	Format                         string `json:"format"`
	SampleRate                     int    `json:"sample_rate"`
	EnableIntermediateResult       bool   `json:"enable_intermediate_result"`
	EnablePunctuationPrediction    bool   `json:"enable_punctuation_prediction"`
	EnableInverseTextNormalization bool   `json:"enable_inverse_text_normalization"`
}

type AsrResult struct {
	Result string `json:"result"`
}

// WAV文件头结构
type WavHeader struct {
	SampleRate    uint32
	Channels      uint16
	BitsPerSample uint16
	DataOffset    int
}

// AliAsrDemo 结构体
type AliAsrDemo struct {
	appkey                 string
	token                  string
	conn                   *websocket.Conn
	isConnected            bool
	isTranscriptionStarted bool
	finalResult            strings.Builder
	currentTaskID          string // 保存当前会话的task_id
}

// 生成32位十六进制字符串
func generateHexID() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// 创建新的ASR实例
func NewAliAsrDemo(appkey, token string) *AliAsrDemo {
	return &AliAsrDemo{
		appkey: appkey,
		token:  token,
	}
}

// 连接WebSocket
func (asr *AliAsrDemo) connectWebSocket() error {
	socketURL := fmt.Sprintf("wss://nls-gateway.cn-shanghai.aliyuncs.com/ws/v1?token=%s", asr.token)

	fmt.Println("正在连接阿里云ASR服务...")

	u, err := url.Parse(socketURL)
	if err != nil {
		return fmt.Errorf("解析URL失败: %v", err)
	}

	conn, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		return fmt.Errorf("WebSocket连接失败: %v", err)
	}

	asr.conn = conn
	asr.isConnected = true
	fmt.Println("✅ 已连接到阿里云ASR服务")

	// 发送开始转录消息
	taskID := generateHexID()
	messageID := generateHexID()

	// 保存task_id供后续使用
	asr.currentTaskID = taskID

	fmt.Printf("🔑 生成的Task ID: %s (长度: %d)\n", taskID, len(taskID))
	fmt.Printf("🔑 生成的Message ID: %s (长度: %d)\n", messageID, len(messageID))

	startMessage := AsrMessage{
		Header: MessageHeader{
			AppKey:    asr.appkey,
			Namespace: "SpeechTranscriber",
			Name:      "StartTranscription",
			TaskID:    taskID,
			MessageID: messageID,
		},
		Payload: StartTranscriptionPayload{
			Format:                         "pcm",
			SampleRate:                     16000,
			EnableIntermediateResult:       true,
			EnablePunctuationPrediction:    true,
			EnableInverseTextNormalization: true,
		},
	}

	if err := asr.conn.WriteJSON(startMessage); err != nil {
		return fmt.Errorf("发送开始转录消息失败: %v", err)
	}

	fmt.Println("📤 已发送开始转录请求")
	return nil
}

// 处理WebSocket消息
func (asr *AliAsrDemo) handleMessages() {
	for {
		_, message, err := asr.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket错误: %v", err)
			}
			break
		}

		var asrMsg AsrMessage
		if err := json.Unmarshal(message, &asrMsg); err != nil {
			fmt.Printf("❌ 解析服务端消息失败: %v\n", err)
			continue
		}

		fmt.Printf("📥 收到服务端消息: %s\n", asrMsg.Header.Name)

		switch asrMsg.Header.Name {
		case "TranscriptionStarted":
			fmt.Println("🎯 转录服务已启动")
			asr.isTranscriptionStarted = true

		case "SentenceEnd":
			if payload, ok := asrMsg.Payload.(map[string]interface{}); ok {
				if result, exists := payload["result"].(string); exists && result != "" {
					fmt.Printf("📝 识别结果: %s\n", result)
					asr.finalResult.WriteString(result + " ")
				}
			}

		case "TranscriptionResultChanged":
			if payload, ok := asrMsg.Payload.(map[string]interface{}); ok {
				if result, exists := payload["result"].(string); exists && result != "" {
					fmt.Printf("🔄 中间结果: %s\n", result)
				}
			}

		case "TranscriptionCompleted":
			fmt.Println("✅ 转录完成")
			fmt.Printf("🎉 最终识别结果: %s\n", strings.TrimSpace(asr.finalResult.String()))

		case "TaskFailed":
			fmt.Printf("❌ 任务失败: %s\n", asrMsg.Header.StatusText)
		}
	}
}

// 解析WAV文件头
func parseWavHeader(data []byte) (*WavHeader, error) {
	if len(data) < 44 {
		return nil, fmt.Errorf("WAV文件头太短")
	}

	header := &WavHeader{
		SampleRate:    binary.LittleEndian.Uint32(data[24:28]),
		Channels:      binary.LittleEndian.Uint16(data[22:24]),
		BitsPerSample: binary.LittleEndian.Uint16(data[34:36]),
		DataOffset:    44, // 标准WAV文件头长度
	}

	// 查找data chunk的实际位置
	offset := 12
	for offset < len(data)-8 {
		chunkID := string(data[offset : offset+4])
		chunkSize := binary.LittleEndian.Uint32(data[offset+4 : offset+8])

		if chunkID == "data" {
			header.DataOffset = offset + 8
			break
		}

		offset += 8 + int(chunkSize)
	}

	return header, nil
}

// 处理WAV文件
func (asr *AliAsrDemo) processWavFile(filePath string) ([]byte, error) {
	fmt.Printf("📁 正在读取音频文件: %s\n", filePath)

	// 读取WAV文件
	wavData, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取文件失败: %v", err)
	}

	// 解析WAV文件头
	header, err := parseWavHeader(wavData)
	if err != nil {
		return nil, fmt.Errorf("解析WAV文件头失败: %v", err)
	}

	fmt.Printf("🎵 音频信息: %dHz, %d声道, %d位\n", header.SampleRate, header.Channels, header.BitsPerSample)

	// 获取PCM数据
	pcmData := wavData[header.DataOffset:]

	fmt.Printf("🎵 音频文件大小: %d 字节\n", len(wavData))
	fmt.Printf("🎵 PCM数据大小: %d 字节\n", len(pcmData))

	if header.BitsPerSample != 16 {
		fmt.Println("⚠️  警告: 音频不是16位PCM格式，可能影响识别效果")
	}

	return pcmData, nil
}

// 发送音频数据
func (asr *AliAsrDemo) sendAudioData(pcmData []byte) error {
	if !asr.isConnected || !asr.isTranscriptionStarted {
		return fmt.Errorf("WebSocket未连接或转录服务未启动")
	}

	fmt.Println("🎤 开始发送音频数据...")

	// 分块发送音频数据（每次4096字节，对应2048个16位样本）
	chunkSize := 4096
	offset := 0

	for offset < len(pcmData) {
		end := offset + chunkSize
		if end > len(pcmData) {
			end = len(pcmData)
		}

		chunk := pcmData[offset:end]

		if err := asr.conn.WriteMessage(websocket.BinaryMessage, chunk); err != nil {
			return fmt.Errorf("发送音频数据失败: %v", err)
		}

		fmt.Printf("📤 发送音频块: %d/%d 字节\n", end, len(pcmData))
		offset = end

		// 模拟实时发送，添加延迟（约128ms，对应2048样本@16kHz）
		time.Sleep(128 * time.Millisecond)
	}

	fmt.Println("✅ 音频数据发送完成")

	// 发送结束信号
	time.Sleep(1 * time.Second)
	stopMessage := AsrMessage{
		Header: MessageHeader{
			AppKey:    asr.appkey,
			Namespace: "SpeechTranscriber",
			Name:      "StopTranscription",
			TaskID:    asr.currentTaskID, // 使用相同的task_id
			MessageID: generateHexID(),   // message_id可以不同
		},
	}

	if err := asr.conn.WriteJSON(stopMessage); err != nil {
		return fmt.Errorf("发送停止转录消息失败: %v", err)
	}

	fmt.Println("📤 已发送停止转录请求")
	return nil
}

// 关闭连接
func (asr *AliAsrDemo) close() {
	if asr.conn != nil {
		asr.conn.Close()
	}
}

// 主要的识别方法
func (asr *AliAsrDemo) recognizeWavFile(filePath string) (string, error) {
	// 1. 连接WebSocket
	if err := asr.connectWebSocket(); err != nil {
		return "", err
	}
	defer asr.close()

	// 启动消息处理协程
	go asr.handleMessages()

	// 2. 等待转录服务启动
	for !asr.isTranscriptionStarted {
		time.Sleep(100 * time.Millisecond)
	}

	// 3. 处理音频文件
	pcmData, err := asr.processWavFile(filePath)
	if err != nil {
		return "", err
	}

	// 4. 发送音频数据
	if err := asr.sendAudioData(pcmData); err != nil {
		return "", err
	}

	// 5. 等待识别完成
	time.Sleep(3 * time.Second)

	return strings.TrimSpace(asr.finalResult.String()), nil
}

func main() {
	// 配置阿里云ASR参数
	const APPKEY = "bbXcKMws8KuybkdP"                // 请替换为您的AppKey
	const TOKEN = "7b1417271c6e4b86914d207a4d599d5a" // 请替换为您的Token

	// 检查参数
	if APPKEY == "your_appkey_here" || TOKEN == "your_token_here" {
		fmt.Println("❌ 请先配置正确的AppKey和Token")
		fmt.Println("💡 请编辑文件中的APPKEY和TOKEN常量")
		os.Exit(1)
	}

	// 获取命令行参数中的音频文件路径
	if len(os.Args) < 2 {
		fmt.Println("❌ 请提供音频文件路径")
		fmt.Println("💡 使用方法: go run ali_asr_demo.go <音频文件路径>")
		fmt.Println("💡 示例: go run ali_asr_demo.go ./test.wav")
		os.Exit(1)
	}

	audioFile := os.Args[1]

	fmt.Println("🚀 阿里云语音识别Demo启动")
	fmt.Printf("📁 音频文件: %s\n", audioFile)

	asrDemo := NewAliAsrDemo(APPKEY, TOKEN)

	result, err := asrDemo.recognizeWavFile(audioFile)
	if err != nil {
		fmt.Printf("❌ 识别失败: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("\n🎉 识别完成！")
	if result != "" {
		fmt.Printf("📝 最终结果: %s\n", result)
	} else {
		fmt.Println("📝 最终结果: 未识别到内容")
	}
}
