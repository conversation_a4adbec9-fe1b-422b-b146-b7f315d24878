# 简单语音识别 Demo

这是一个基于阿里云语音识别服务的简单 WAV 文件上传识别 demo。

## 功能特点

- ✅ 简单易用的 Web 界面
- ✅ 支持 WAV 文件拖拽上传
- ✅ 基于阿里云语音识别服务
- ✅ 实时显示识别结果
- ✅ 错误处理和用户友好提示

## 文件说明

- `simple_asr_demo.js` - 简化的语音识别服务器
- `upload.html` - 文件上传页面
- `README_simple_demo.md` - 本说明文档

## 安装和配置

### 1. 安装依赖

确保已安装必要的 npm 包：

```bash
npm install express multer ws uuid
```

### 2. 配置阿里云凭证

编辑 `simple_asr_demo.js` 文件，替换以下配置：

```javascript
const ALIYUN_CONFIG = {
    appkey: 'your_appkey_here',  // 替换为您的appkey
    token: 'your_token_here'     // 替换为您的token
};
```

### 3. 获取阿里云凭证

1. 登录阿里云控制台
2. 开通智能语音交互服务
3. 创建项目获取 AppKey
4. 生成访问令牌 Token

## 运行方法

1. 启动服务器：
```bash
node simple_asr_demo.js
```

2. 打开浏览器访问：
```
http://localhost:3000
```

3. 上传 WAV 文件进行语音识别

## 使用步骤

1. 准备 WAV 格式的音频文件
2. 在网页中点击"选择文件"或直接拖拽文件到上传区域
3. 点击"开始识别"按钮
4. 等待识别完成，查看结果

## 音频文件要求

- 格式：WAV
- 采样率：建议 16kHz
- 声道：单声道或双声道
- 时长：建议不超过 60 秒

## 注意事项

1. 确保网络连接正常，能够访问阿里云服务
2. 音频文件质量会影响识别准确率
3. 服务器会自动清理上传的临时文件
4. 识别超时时间设置为 30 秒

## 错误排查

### 常见问题

1. **"请确保已正确配置阿里云ASR的appkey和token"**
   - 检查 `ALIYUN_CONFIG` 中的配置是否正确

2. **"WebSocket错误"**
   - 检查网络连接
   - 确认 token 是否有效且未过期

3. **"未识别到语音内容"**
   - 检查音频文件是否包含清晰的语音
   - 确认音频格式是否正确

4. **"识别超时"**
   - 音频文件可能过长
   - 网络连接可能不稳定

## 与原版本的区别

相比原始的 `asr_ali_ws.js`：

- ❌ 移除了实时录音功能
- ❌ 移除了麦克风依赖
- ✅ 添加了文件上传功能
- ✅ 添加了 Web 界面
- ✅ 简化了代码结构
- ✅ 更好的错误处理

## 扩展建议

如需更多功能，可以考虑：

1. 支持更多音频格式（MP3、M4A等）
2. 添加音频格式转换功能
3. 支持批量文件处理
4. 添加识别历史记录
5. 集成其他语音识别服务

## 技术栈

- **后端**: Node.js + Express
- **前端**: HTML + CSS + JavaScript
- **文件上传**: Multer
- **WebSocket**: ws
- **语音识别**: 阿里云智能语音交互
