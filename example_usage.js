#!/usr/bin/env node

/**
 * 阿里云ASR Demo 使用示例
 * 
 * 这个脚本展示了如何使用 ali_asr_demo.js 进行语音识别
 */

const AliAsrDemo = require('./ali_asr_demo');
const fs = require('fs');
const path = require('path');

// 配置信息
const CONFIG = {
    // 阿里云ASR配置 - 请替换为您的实际配置
    APPKEY: 'your_appkey_here',
    TOKEN: 'your_token_here',
    
    // 测试音频文件列表
    TEST_FILES: [
        './test1.wav',
        './test2.wav',
        './sample.wav'
    ]
};

/**
 * 单个文件识别示例
 */
async function recognizeSingleFile(filePath) {
    console.log(`\n🎯 开始识别文件: ${filePath}`);
    
    if (!fs.existsSync(filePath)) {
        console.error(`❌ 文件不存在: ${filePath}`);
        return null;
    }
    
    const asrDemo = new AliAsrDemo(CONFIG.APPKEY, CONFIG.TOKEN);
    
    try {
        const result = await asrDemo.recognizeWavFile(filePath);
        console.log(`✅ 识别完成: ${result || '未识别到内容'}`);
        return result;
    } catch (error) {
        console.error(`❌ 识别失败: ${error.message}`);
        return null;
    }
}

/**
 * 批量文件识别示例
 */
async function recognizeMultipleFiles(filePaths) {
    console.log('\n📁 开始批量识别...');
    
    const results = [];
    
    for (const filePath of filePaths) {
        if (fs.existsSync(filePath)) {
            const result = await recognizeSingleFile(filePath);
            results.push({
                file: filePath,
                result: result
            });
            
            // 添加延迟，避免请求过于频繁
            await new Promise(resolve => setTimeout(resolve, 2000));
        } else {
            console.log(`⚠️  跳过不存在的文件: ${filePath}`);
        }
    }
    
    return results;
}

/**
 * 显示识别结果摘要
 */
function displayResults(results) {
    console.log('\n📊 识别结果摘要:');
    console.log('=' .repeat(50));
    
    results.forEach((item, index) => {
        console.log(`${index + 1}. 文件: ${path.basename(item.file)}`);
        console.log(`   结果: ${item.result || '未识别到内容'}`);
        console.log('');
    });
}

/**
 * 检查配置
 */
function checkConfiguration() {
    if (CONFIG.APPKEY === 'your_appkey_here' || CONFIG.TOKEN === 'your_token_here') {
        console.error('❌ 请先配置正确的AppKey和Token');
        console.log('💡 请编辑 example_usage.js 中的 CONFIG 对象');
        console.log('💡 将 APPKEY 和 TOKEN 替换为您的实际值');
        return false;
    }
    return true;
}

/**
 * 显示帮助信息
 */
function showHelp() {
    console.log('🔧 阿里云ASR Demo 使用示例');
    console.log('');
    console.log('使用方法:');
    console.log('  node example_usage.js [选项] [文件路径]');
    console.log('');
    console.log('选项:');
    console.log('  --help, -h     显示帮助信息');
    console.log('  --batch, -b    批量处理模式');
    console.log('  --test, -t     使用内置测试文件');
    console.log('');
    console.log('示例:');
    console.log('  node example_usage.js ./audio.wav');
    console.log('  node example_usage.js --batch ./audio1.wav ./audio2.wav');
    console.log('  node example_usage.js --test');
}

/**
 * 主函数
 */
async function main() {
    const args = process.argv.slice(2);
    
    // 显示帮助
    if (args.includes('--help') || args.includes('-h')) {
        showHelp();
        return;
    }
    
    // 检查配置
    if (!checkConfiguration()) {
        return;
    }
    
    console.log('🚀 阿里云ASR Demo 使用示例');
    
    try {
        if (args.includes('--test') || args.includes('-t')) {
            // 测试模式
            console.log('🧪 测试模式: 使用内置测试文件');
            const results = await recognizeMultipleFiles(CONFIG.TEST_FILES);
            displayResults(results);
            
        } else if (args.includes('--batch') || args.includes('-b')) {
            // 批量模式
            const files = args.filter(arg => !arg.startsWith('--') && !arg.startsWith('-'));
            if (files.length === 0) {
                console.error('❌ 批量模式需要指定至少一个文件');
                return;
            }
            
            const results = await recognizeMultipleFiles(files);
            displayResults(results);
            
        } else if (args.length > 0) {
            // 单文件模式
            const filePath = args[0];
            await recognizeSingleFile(filePath);
            
        } else {
            // 默认行为
            console.log('💡 请指定音频文件路径或使用 --help 查看帮助');
            console.log('💡 示例: node example_usage.js ./test.wav');
        }
        
    } catch (error) {
        console.error('❌ 程序执行出错:', error.message);
    }
}

// 运行主函数
if (require.main === module) {
    main().catch(console.error);
}

module.exports = {
    recognizeSingleFile,
    recognizeMultipleFiles,
    CONFIG
};
