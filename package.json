{"name": "googletranslate", "version": "1.0.0", "description": "这是一个集成了语音识别(ASR)、机器翻译和文本到语音(TTS)功能的Node.js服务器。", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@google-cloud/speech": "^6.7.0", "@google-cloud/storage": "^7.14.0", "@google-cloud/text-to-speech": "^5.7.0", "axios": "^1.8.4", "crypto": "^1.0.1", "crypto-js": "^4.2.0", "express": "^4.21.2", "fluent-ffmpeg": "^2.1.3", "https-proxy-agent": "^7.0.6", "log4node": "^0.1.6", "microsoft-cognitiveservices-speech-sdk": "^1.42.0", "multer": "^2.0.1", "openai": "^4.77.4", "request": "^2.88.2", "uuid": "^11.1.0", "ws": "^8.18.0"}}