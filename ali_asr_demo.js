const fs = require('fs');
const WebSocket = require('ws');
const path = require('path');
const crypto = require('crypto');

class AliAsrDemo {
    constructor(appkey, token) {
        this.appkey = appkey;
        this.token = token;
        this.websocket = null;
        this.taskId = this.generateUUID();
        this.messageId = this.generateUUID();
        this.isConnected = false;
        this.isTranscriptionStarted = false;
        this.finalResult = '';
    }

    // 生成32位十六进制字符串作为message_id（阿里云要求）
    generateMessageId() {
        // 生成32个十六进制字符
        return crypto.randomBytes(16).toString('hex');
    }

    // 生成task_id（也使用32位十六进制）
    generateTaskId() {
        return crypto.randomBytes(16).toString('hex');
    }

    // 连接WebSocket
    async connectWebSocket() {
        return new Promise((resolve, reject) => {
            const socketUrl = `wss://nls-gateway.cn-shanghai.aliyuncs.com/ws/v1?token=${this.token}`;
            
            console.log('正在连接阿里云ASR服务...');
            this.websocket = new WebSocket(socketUrl);

            this.websocket.on('open', () => {
                console.log('✅ 已连接到阿里云ASR服务');
                this.isConnected = true;

                // 发送开始转录消息
                const taskId = this.generateTaskId();
                const messageId = this.generateMessageId();

                console.log(`🔑 生成的Task ID: ${taskId} (长度: ${taskId.length})`);
                console.log(`🔑 生成的Message ID: ${messageId} (长度: ${messageId.length})`);

                const startTranscriptionMessage = {
                    header: {
                        appkey: this.appkey,
                        namespace: "SpeechTranscriber",
                        name: "StartTranscription",
                        task_id: taskId,
                        message_id: messageId
                    },
                    payload: {
                        "format": "pcm",
                        "sample_rate": 16000,
                        "enable_intermediate_result": true,
                        "enable_punctuation_prediction": true,
                        "enable_inverse_text_normalization": true
                    }
                };

                this.websocket.send(JSON.stringify(startTranscriptionMessage));
                console.log('📤 已发送开始转录请求');
            });

            this.websocket.on('message', (data) => {
                try {
                    const message = JSON.parse(data.toString());
                    console.log('📥 收到服务端消息:', JSON.stringify(message));

                    if (message.header.name === "TranscriptionStarted") {
                        console.log('🎯 转录服务已启动');
                        this.isTranscriptionStarted = true;
                        resolve();
                    } else if (message.header.name === "SentenceEnd") {
                        if (message.payload && message.payload.result) {
                            console.log('📝 识别结果:', message.payload.result);
                            this.finalResult += message.payload.result + ' ';
                        }
                    } else if (message.header.name === "TranscriptionResultChanged") {
                        if (message.payload && message.payload.result) {
                            console.log('🔄 中间结果:', message.payload.result);
                        }
                    } else if (message.header.name === "TranscriptionCompleted") {
                        console.log('✅ 转录完成');
                        console.log('🎉 最终识别结果:', this.finalResult.trim());
                    }
                } catch (error) {
                    console.error('❌ 解析服务端消息失败:', error);
                }
            });

            this.websocket.on('error', (error) => {
                console.error('❌ WebSocket错误:', error);
                reject(error);
            });

            this.websocket.on('close', () => {
                console.log('🔌 WebSocket连接已关闭');
                this.isConnected = false;
            });
        });
    }

    // 读取WAV文件并转换为PCM数据
    async processWavFile(filePath) {
        if (!fs.existsSync(filePath)) {
            throw new Error(`文件不存在: ${filePath}`);
        }

        console.log(`📁 正在读取音频文件: ${filePath}`);

        // 读取WAV文件
        const wavBuffer = fs.readFileSync(filePath);

        // 解析WAV文件头部信息
        const wavHeader = this.parseWavHeader(wavBuffer);
        console.log(`🎵 音频信息: ${wavHeader.sampleRate}Hz, ${wavHeader.channels}声道, ${wavHeader.bitsPerSample}位`);

        // 跳过WAV文件头，获取PCM数据
        const pcmData = wavBuffer.slice(wavHeader.dataOffset);

        console.log(`🎵 音频文件大小: ${wavBuffer.length} 字节`);
        console.log(`🎵 PCM数据大小: ${pcmData.length} 字节`);

        // 如果不是16位PCM，需要转换
        if (wavHeader.bitsPerSample !== 16) {
            console.log('⚠️  警告: 音频不是16位PCM格式，可能影响识别效果');
        }

        return pcmData;
    }

    // 解析WAV文件头部
    parseWavHeader(buffer) {
        const header = {
            sampleRate: buffer.readUInt32LE(24),
            channels: buffer.readUInt16LE(22),
            bitsPerSample: buffer.readUInt16LE(34),
            dataOffset: 44 // 标准WAV文件头长度
        };

        // 查找data chunk的实际位置
        let offset = 12;
        while (offset < buffer.length - 8) {
            const chunkId = buffer.toString('ascii', offset, offset + 4);
            const chunkSize = buffer.readUInt32LE(offset + 4);

            if (chunkId === 'data') {
                header.dataOffset = offset + 8;
                break;
            }

            offset += 8 + chunkSize;
        }

        return header;
    }

    // 发送音频数据
    async sendAudioData(pcmData) {
        if (!this.isConnected || !this.isTranscriptionStarted) {
            throw new Error('WebSocket未连接或转录服务未启动');
        }

        console.log('🎤 开始发送音频数据...');

        // 分块发送音频数据（每次4096字节，对应2048个16位样本）
        const chunkSize = 4096; // 2048 samples * 2 bytes per sample
        let offset = 0;

        while (offset < pcmData.length) {
            const chunk = pcmData.slice(offset, offset + chunkSize);

            if (this.websocket.readyState === WebSocket.OPEN) {
                // 直接发送二进制数据，与HTML版本保持一致
                this.websocket.send(chunk);
                console.log(`📤 发送音频块: ${offset + chunk.length}/${pcmData.length} 字节`);
            }

            offset += chunkSize;

            // 模拟实时发送，添加延迟（约128ms，对应2048样本@16kHz）
            await new Promise(resolve => setTimeout(resolve, 128));
        }

        console.log('✅ 音频数据发送完成');

        // 发送结束信号
        setTimeout(() => {
            if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
                const stopMessage = {
                    header: {
                        appkey: this.appkey,
                        namespace: "SpeechTranscriber",
                        name: "StopTranscription",
                        task_id: this.generateTaskId(),
                        message_id: this.generateMessageId()
                    }
                };
                this.websocket.send(JSON.stringify(stopMessage));
                console.log('📤 已发送停止转录请求');
            }
        }, 1000);
    }

    // 关闭连接
    close() {
        if (this.websocket) {
            this.websocket.close();
        }
    }

    // 主要的识别方法
    async recognizeWavFile(filePath) {
        try {
            // 1. 连接WebSocket
            await this.connectWebSocket();
            
            // 2. 等待转录服务启动
            while (!this.isTranscriptionStarted) {
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            // 3. 处理音频文件
            const pcmData = await this.processWavFile(filePath);
            
            // 4. 发送音频数据
            await this.sendAudioData(pcmData);
            
            // 5. 等待识别完成
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // 6. 关闭连接
            this.close();
            
            return this.finalResult.trim();
            
        } catch (error) {
            console.error('❌ 识别过程中发生错误:', error);
            this.close();
            throw error;
        }
    }
}

// 使用示例
async function main() {
    // 配置阿里云ASR参数
    const APPKEY = 'bbXcKMws8KuybkdP';  // 请替换为您的AppKey
    const TOKEN = '7b1417271c6e4b86914d207a4d599d5a';    // 请替换为您的Token
    
    // 检查参数
    if (APPKEY === 'your_appkey_here' || TOKEN === 'your_token_here') {
        console.error('❌ 请先配置正确的AppKey和Token');
        console.log('💡 请编辑文件中的APPKEY和TOKEN变量');
        process.exit(1);
    }
    
    // 获取命令行参数中的音频文件路径
    const audioFile = process.argv[2];
    if (!audioFile) {
        console.error('❌ 请提供音频文件路径');
        console.log('💡 使用方法: node ali_asr_demo.js <音频文件路径>');
        console.log('💡 示例: node ali_asr_demo.js ./test.wav');
        process.exit(1);
    }
    
    console.log('🚀 阿里云语音识别Demo启动');
    console.log('📁 音频文件:', audioFile);
    
    const asrDemo = new AliAsrDemo(APPKEY, TOKEN);
    
    try {
        const result = await asrDemo.recognizeWavFile(audioFile);
        console.log('\n🎉 识别完成！');
        console.log('📝 最终结果:', result || '未识别到内容');
    } catch (error) {
        console.error('❌ 识别失败:', error.message);
        process.exit(1);
    }
}

// 如果直接运行此文件，则执行main函数
if (require.main === module) {
    main().catch(console.error);
}

module.exports = AliAsrDemo;
