# 阿里云语音识别 Node.js Demo

这是一个基于阿里云语音识别服务的简单Node.js命令行工具，可以识别本地WAV音频文件中的语音内容。

## 功能特点

- ✅ 支持本地WAV文件语音识别
- ✅ 基于阿里云智能语音交互服务
- ✅ WebSocket实时通信
- ✅ 支持中间结果和最终结果显示
- ✅ 简单易用的命令行界面

## 文件说明

- `ali_asr_demo.js` - 主要的ASR demo文件（核心实现）
- `test_ali_asr.js` - 测试脚本
- `example_usage.js` - 使用示例脚本（支持单文件和批量处理）
- `README_ali_asr_demo.md` - 本说明文档

## 安装和配置

### 1. 确认依赖

项目已包含必要的依赖包：
- `ws` - WebSocket客户端
- `fs` - 文件系统操作（Node.js内置）

### 2. 配置阿里云凭证

编辑 `ali_asr_demo.js` 文件，找到以下配置并替换为您的实际值：

```javascript
// 配置阿里云ASR参数
const APPKEY = 'your_appkey_here';  // 请替换为您的AppKey
const TOKEN = 'your_token_here';    // 请替换为您的Token
```

### 3. 获取阿里云凭证

1. 登录[阿里云控制台](https://ecs.console.aliyun.com/)
2. 开通[智能语音交互服务](https://nls.console.aliyun.com/)
3. 创建项目获取 AppKey
4. 生成访问令牌 Token

## 使用方法

### 方法一：直接使用

```bash
node ali_asr_demo.js <音频文件路径>
```

### 方法二：使用npm脚本

```bash
# 基本使用
npm run asr-demo <音频文件路径>

# 运行测试
npm run asr-test

# 使用示例脚本
npm run asr-example <音频文件路径>
```

### 方法三：使用示例脚本

```bash
# 单文件识别
node example_usage.js ./test.wav

# 批量识别
node example_usage.js --batch ./audio1.wav ./audio2.wav

# 测试模式
node example_usage.js --test

# 查看帮助
node example_usage.js --help
```

### 具体示例

```bash
# 识别当前目录下的test.wav文件
node ali_asr_demo.js ./test.wav

# 识别指定路径的音频文件
node ali_asr_demo.js /path/to/your/audio.wav

# 使用npm脚本
npm run asr-demo ./test.wav
```

## 音频文件要求

- **格式**: WAV
- **采样率**: 16kHz（推荐）
- **声道**: 单声道或双声道
- **编码**: PCM
- **时长**: 建议不超过60秒

## 输出示例

```
🚀 阿里云语音识别Demo启动
📁 音频文件: ./test.wav
正在连接阿里云ASR服务...
✅ 已连接到阿里云ASR服务
📤 已发送开始转录请求
📥 收到服务端消息: TranscriptionStarted
🎯 转录服务已启动
📁 正在读取音频文件: ./test.wav
🎵 音频文件大小: 176444 字节
🎵 PCM数据大小: 176400 字节
🎤 开始发送音频数据...
📤 发送音频块: 2048/176400 字节
📤 发送音频块: 4096/176400 字节
...
🔄 中间结果: 你好
📝 识别结果: 你好，这是一个语音识别测试。
✅ 音频数据发送完成
📤 已发送停止转录请求
📥 收到服务端消息: TranscriptionCompleted
✅ 转录完成
🎉 最终识别结果: 你好，这是一个语音识别测试。

🎉 识别完成！
📝 最终结果: 你好，这是一个语音识别测试。
```

## 错误排查

### 常见问题

1. **"请先配置正确的AppKey和Token"**
   - 检查代码中的 `APPKEY` 和 `TOKEN` 是否已正确配置

2. **"文件不存在"**
   - 确认音频文件路径是否正确
   - 检查文件是否存在且可读

3. **"WebSocket错误"**
   - 检查网络连接
   - 确认Token是否有效且未过期
   - 检查阿里云服务是否正常

4. **"未识别到内容"**
   - 检查音频文件是否包含清晰的语音
   - 确认音频格式是否符合要求
   - 尝试调整音频质量

## 技术实现

### 核心流程

1. **建立WebSocket连接** - 连接到阿里云ASR服务
2. **发送开始转录请求** - 初始化转录会话
3. **读取WAV文件** - 跳过文件头，提取PCM数据
4. **分块发送音频** - 将PCM数据分块发送到服务端
5. **接收识别结果** - 处理中间结果和最终结果
6. **发送停止请求** - 结束转录会话

### 与原HTML版本的区别

- ❌ 移除了浏览器相关的API（AudioContext、MediaDevices等）
- ❌ 移除了实时录音功能
- ✅ 添加了文件读取功能
- ✅ 添加了命令行参数处理
- ✅ 简化了音频处理逻辑
- ✅ 保持了WebSocket通信协议

## 扩展建议

如需更多功能，可以考虑：

1. 支持更多音频格式（MP3、M4A等）
2. 添加音频格式转换功能
3. 支持批量文件处理
4. 添加配置文件支持
5. 集成其他语音识别服务
6. 添加音频预处理功能

## 注意事项

1. 确保网络连接稳定，能够访问阿里云服务
2. 音频文件质量会影响识别准确率
3. Token有有效期限制，需要定期更新
4. 大文件可能需要较长的处理时间
5. 本demo使用简化的WAV文件处理，可能不适用于所有WAV格式

## 许可证

本项目仅供学习和测试使用。
